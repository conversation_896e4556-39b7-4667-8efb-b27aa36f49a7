"use client"

import { useState } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface Book {
  id: string
  title: string
  description: string
  cover_image_url: string
  price_amount: number
  genre: string
  book_type?: string
  average_rating?: number
  review_count?: number
  sales_count?: number
  created_at: string
  slug?: string
  author_name?: string
  users: {
    name: string
    avatar_url: string
  }
}

interface BookCardProps {
  book: Book
  showShareButton?: boolean
  priority?: boolean
}

export function BookCard({ book, showShareButton = true, priority = false }: BookCardProps) {
  const [imageError, setImageError] = useState(false)
  const [shareMenuOpen, setShareMenuOpen] = useState(false)

  const formatPrice = (cents: number) => {
    if (cents === 0) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }

  const renderPenRating = (rating: number = 0, reviewCount: number = 0) => {
    if (reviewCount === 0 || !rating) {
      return (
        <div className="flex items-center gap-1 text-xs text-gray-400">
          <span>No ratings yet</span>
        </div>
      )
    }
    
    const fullPens = Math.floor(rating)
    const hasHalfPen = rating % 1 >= 0.5
    
    return (
      <div className="flex items-center gap-1">
        <div className="flex">
          {[...Array(10)].map((_, i) => (
            <span
              key={i}
              className={`text-xs ${
                i < fullPens 
                  ? 'text-purple-600' 
                  : i === fullPens && hasHalfPen 
                    ? 'text-purple-400' 
                    : 'text-gray-300'
              }`}
            >
              🖊️
            </span>
          ))}
        </div>
        <span className="text-xs text-gray-600 ml-1">
          {rating.toFixed(1)} ({reviewCount})
        </span>
      </div>
    )
  }

  const getBookUrl = () => {
    return book.slug ? `/books/${book.slug}` : `/books/${book.id}`
  }

  const getShareUrl = () => {
    return `${window.location.origin}${getBookUrl()}`
  }

  const handleShare = async (platform: string) => {
    const url = getShareUrl()
    const text = `Check out "${book.title}" by ${book.users.name} on OnlyDiary`
    
    switch (platform) {
      case 'copy':
        try {
          await navigator.clipboard.writeText(url)
          alert('Link copied to clipboard!')
        } catch (err) {
          // Fallback for older browsers
          const textArea = document.createElement('textarea')
          textArea.value = url
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
          alert('Link copied to clipboard!')
        }
        break
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank')
        break
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank')
        break
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank')
        break
      case 'email':
        window.open(`mailto:?subject=${encodeURIComponent(`Check out "${book.title}"`)}&body=${encodeURIComponent(`${text}\n\n${url}`)}`)
        break
    }
    setShareMenuOpen(false)
  }

  const getBadges = () => {
    const badges = []
    
    // New release badge (within 30 days)
    const daysSinceRelease = Math.floor((Date.now() - new Date(book.created_at).getTime()) / (1000 * 60 * 60 * 24))
    if (daysSinceRelease <= 30) {
      badges.push({ text: 'New', color: 'bg-green-500' })
    }
    
    // Bestseller badge (more than 50 sales)
    if ((book.sales_count || 0) > 50) {
      badges.push({ text: 'Bestseller', color: 'bg-purple-500' })
    }

    // Highly rated badge (4.5+ rating with 10+ reviews)
    if ((book.average_rating || 0) >= 4.5 && (book.review_count || 0) >= 10) {
      badges.push({ text: 'Highly Rated', color: 'bg-yellow-500' })
    }
    
    // Free badge
    if (book.price_amount === 0) {
      badges.push({ text: 'Free', color: 'bg-blue-500' })
    }
    
    return badges
  }

  const badges = getBadges()

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 hover:scale-[1.02] cursor-pointer h-full relative">
      
      {/* Share Button */}
      {showShareButton && (
        <div className="absolute top-2 left-2 z-10">
          <div className="relative">
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                setShareMenuOpen(!shareMenuOpen)
              }}
              className="bg-white/90 backdrop-blur-sm border-gray-200 hover:bg-white text-gray-600 hover:text-gray-900 p-2 h-8 w-8"
            >
              🔗
            </Button>
            
            {shareMenuOpen && (
              <div className="absolute top-full left-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[160px] z-20">
                <button
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleShare('copy')
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  📋 Copy Link
                </button>
                <button
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleShare('twitter')
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  🐦 Twitter
                </button>
                <button
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleShare('facebook')
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  📘 Facebook
                </button>
                <button
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleShare('email')
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  ✉️ Email
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      <Link href={getBookUrl()}>
        {/* Book Cover */}
        <div className="aspect-[3/4] relative overflow-hidden rounded-t-lg bg-gradient-to-br from-purple-100 to-blue-100">
          {book.cover_image_url && !imageError ? (
            <img
              src={book.cover_image_url}
              alt={book.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              onError={() => setImageError(true)}
              loading={priority ? "eager" : "lazy"}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-4xl">
              📖
            </div>
          )}
          
          {/* Badges */}
          {badges.length > 0 && (
            <div className="absolute top-2 right-2 flex flex-col gap-1">
              {badges.slice(0, 2).map((badge, index) => (
                <span
                  key={index}
                  className={`${badge.color} text-white text-xs px-2 py-1 rounded-full font-medium`}
                >
                  {badge.text}
                </span>
              ))}
            </div>
          )}
          
          {/* Price Badge */}
          <div className="absolute bottom-2 right-2 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium">
            {formatPrice(book.price_amount)}
          </div>
        </div>

        <CardContent className="p-3 sm:p-4 flex-1 flex flex-col">
          {/* Title */}
          <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-purple-600 transition-colors text-sm sm:text-base">
            {book.title}
          </h3>

          {/* Author */}
          <div className="flex items-center gap-2 mb-2">
            {book.users.avatar_url ? (
              <img
                src={book.users.avatar_url}
                alt={book.users.name}
                className="w-4 sm:w-5 h-4 sm:h-5 rounded-full"
              />
            ) : (
              <div className="w-4 sm:w-5 h-4 sm:h-5 bg-gray-200 rounded-full flex items-center justify-center text-xs">
                👤
              </div>
            )}
            <span className="text-xs sm:text-sm text-gray-600 truncate">
              by {book.author_name || book.users.name}
            </span>
          </div>

          {/* Description */}
          {book.description && (
            <p className="text-xs sm:text-sm text-gray-600 mb-3 line-clamp-2 flex-1">
              {book.description}
            </p>
          )}
          
          {/* Rating */}
          <div className="mb-2 sm:mb-3">
            {renderPenRating(book.average_rating || 0, book.review_count || 0)}
          </div>

          {/* Genre & Type */}
          <div className="flex flex-wrap gap-1 mb-2 sm:mb-3">
            {book.genre && (
              <span className="inline-block px-1.5 sm:px-2 py-0.5 sm:py-1 bg-purple-100 text-purple-700 text-xs rounded">
                {book.genre.replace('_', ' ')}
              </span>
            )}
            {book.book_type && book.book_type !== book.genre && (
              <span className="inline-block px-1.5 sm:px-2 py-0.5 sm:py-1 bg-blue-100 text-blue-700 text-xs rounded">
                {book.book_type.replace('_', ' ')}
              </span>
            )}
          </div>

          {/* Sales Count */}
          {(book.sales_count || 0) > 0 && (
            <div className="text-xs text-gray-500 mt-auto">
              {book.sales_count} {book.sales_count === 1 ? 'copy' : 'copies'} sold
            </div>
          )}
        </CardContent>
      </Link>
    </Card>
  )
}
