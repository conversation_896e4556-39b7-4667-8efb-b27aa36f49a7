-- OnlyDiary Database Schema Check
-- Run this in your Supabase SQL editor to check what's missing

-- 1. Check if projects table exists and show its structure
SELECT 'Projects table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'projects' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Check for missing ebook-related columns
SELECT 'Missing ebook columns check:' as info;
SELECT 
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'is_ebook') 
    THEN 'EXISTS' ELSE 'MISSING' END as is_ebook_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'ebook_file_url') 
    THEN 'EXISTS' ELSE 'MISSING' END as ebook_file_url_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'book_type') 
    THEN 'EXISTS' ELSE 'MISSING' END as book_type_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'average_rating') 
    THEN 'EXISTS' ELSE 'MISSING' END as average_rating_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'review_count') 
    THEN 'EXISTS' ELSE 'MISSING' END as review_count_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'sales_count') 
    THEN 'EXISTS' ELSE 'MISSING' END as sales_count_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'tags') 
    THEN 'EXISTS' ELSE 'MISSING' END as tags_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'slug') 
    THEN 'EXISTS' ELSE 'MISSING' END as slug_column,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'author_name') 
    THEN 'EXISTS' ELSE 'MISSING' END as author_name_column;

-- 3. Check current projects data
SELECT 'Current projects in database:' as info;
SELECT id, title, genre, price_amount, is_complete, is_private, user_id, created_at
FROM projects
ORDER BY created_at DESC
LIMIT 10;

-- 4. Check users table structure
SELECT 'Users table structure:' as info;
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'users' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 5. Check storage buckets
SELECT 'Storage buckets:' as info;
SELECT id, name, public
FROM storage.buckets
ORDER BY name;

-- 6. Check RLS policies on projects
SELECT 'Projects RLS policies:' as info;
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE tablename = 'projects';
