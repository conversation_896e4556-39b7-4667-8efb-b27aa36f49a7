-- <PERSON>reate function to update project statistics automatically
CREATE OR <PERSON><PERSON>LACE FUNCTION update_project_stats()
RETURNS TRIGGER AS $$
DECLARE
    project_id_to_update UUID;
    chapter_count INTEGER;
    word_count INTEGER;
BEGIN
    -- Determine which project to update based on the operation
    IF TG_OP = 'DELETE' THEN
        project_id_to_update := OLD.project_id;
    ELSE
        project_id_to_update := NEW.project_id;
    END IF;

    -- Calculate current statistics for the project
    SELECT 
        COUNT(*),
        COALESCE(SUM(word_count), 0)
    INTO 
        chapter_count,
        word_count
    FROM chapters 
    WHERE project_id = project_id_to_update;

    -- Update the project with new statistics
    UPDATE projects 
    SET 
        total_chapters = chapter_count,
        total_words = word_count,
        updated_at = NOW()
    WHERE id = project_id_to_update;

    -- Return the appropriate record
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON> triggers to automatically update project stats
DROP TRIGGER IF EXISTS update_project_stats_on_insert ON chapters;
DROP TRIGGER IF EXISTS update_project_stats_on_update ON chapters;
DROP TRIGGER IF EXISTS update_project_stats_on_delete ON chapters;

CREATE TRIGGER update_project_stats_on_insert
    AFTER INSERT ON chapters
    FOR EACH ROW
    EXECUTE FUNCTION update_project_stats();

CREATE TRIGGER update_project_stats_on_update
    AFTER UPDATE ON chapters
    FOR EACH ROW
    EXECUTE FUNCTION update_project_stats();

CREATE TRIGGER update_project_stats_on_delete
    AFTER DELETE ON chapters
    FOR EACH ROW
    EXECUTE FUNCTION update_project_stats();

-- Update existing projects with correct statistics
UPDATE projects 
SET 
    total_chapters = (
        SELECT COUNT(*) 
        FROM chapters 
        WHERE chapters.project_id = projects.id
    ),
    total_words = (
        SELECT COALESCE(SUM(word_count), 0) 
        FROM chapters 
        WHERE chapters.project_id = projects.id
    )
WHERE EXISTS (
    SELECT 1 
    FROM chapters 
    WHERE chapters.project_id = projects.id
);
