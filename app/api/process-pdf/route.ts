import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { projectId, pdfUrl } = await request.json()

    if (!projectId || !pdfUrl) {
      return NextResponse.json(
        { error: 'Project ID and PDF URL are required' },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify the project belongs to the user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, title, user_id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    // For now, we'll create a simple placeholder implementation
    // In a production environment, you would use a PDF processing library like pdf-parse
    // or call an external service to extract text from the PDF
    
    try {
      // Fetch the PDF file
      const pdfResponse = await fetch(pdfUrl)
      if (!pdfResponse.ok) {
        throw new Error('Failed to fetch PDF file')
      }

      // For now, create a single chapter with placeholder content
      // TODO: Replace this with actual PDF text extraction
      const placeholderContent = `This is a placeholder chapter extracted from your uploaded PDF.

To properly extract text from PDF files, we need to implement PDF processing functionality. 

Your book "${project.title}" has been uploaded successfully, but the content extraction is not yet implemented.

Please manually add chapters through the project editor, or contact support for assistance with PDF processing.`

      // Create a single chapter
      const { data: chapter, error: chapterError } = await supabase
        .from('chapters')
        .insert({
          project_id: projectId,
          user_id: user.id,
          title: 'Chapter 1',
          content: placeholderContent,
          chapter_number: 1,
          word_count: placeholderContent.split(/\s+/).filter(word => word.length > 0).length,
          is_published: true
        })
        .select()
        .single()

      if (chapterError) {
        console.error('Error creating chapter:', chapterError)
        throw new Error('Failed to create chapter')
      }

      // Update project statistics
      const totalWords = placeholderContent.split(/\s+/).filter(word => word.length > 0).length
      
      const { error: updateError } = await supabase
        .from('projects')
        .update({
          total_chapters: 1,
          total_words: totalWords,
          is_complete: true
        })
        .eq('id', projectId)

      if (updateError) {
        console.error('Error updating project stats:', updateError)
        // Don't fail the request for this
      }

      return NextResponse.json({
        success: true,
        message: 'PDF processed successfully',
        chaptersCreated: 1,
        totalWords: totalWords,
        note: 'This is a placeholder implementation. Full PDF text extraction is not yet implemented.'
      })

    } catch (processingError) {
      console.error('PDF processing error:', processingError)
      return NextResponse.json(
        { error: 'Failed to process PDF file' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
