import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { projectId, pdfUrl } = await request.json()

    if (!projectId || !pdfUrl) {
      return NextResponse.json(
        { error: 'Project ID and PDF URL are required' },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify the project belongs to the user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, title, user_id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    // For finished ebooks (PDFs/EPUBs), we don't create individual chapters
    // The book is already complete and should be read as a whole document

    try {
      // Fetch the PDF file to validate it exists
      const pdfResponse = await fetch(pdfUrl)
      if (!pdfResponse.ok) {
        throw new Error('Failed to fetch PDF file')
      }

      // For finished ebooks, we just mark them as processed
      // No chapters are created since this is a complete book
      // The PDF/EPUB file itself serves as the content

      // Estimate word count based on typical PDF page count
      // This is a rough estimate - in production you'd extract actual text
      const estimatedWords = 50000 // Placeholder - typical book length

      const { error: updateError } = await supabase
        .from('projects')
        .update({
          total_chapters: 0, // Finished ebooks don't have individual chapters
          total_words: estimatedWords,
          is_complete: true
        })
        .eq('id', projectId)

      if (updateError) {
        console.error('Error updating project stats:', updateError)
        throw new Error('Failed to update project')
      }

      return NextResponse.json({
        success: true,
        message: 'Ebook processed successfully',
        chaptersCreated: 0,
        totalWords: estimatedWords,
        note: 'Finished ebook processed. No individual chapters created - book should be read as complete document.'
      })

    } catch (processingError) {
      console.error('PDF processing error:', processingError)
      return NextResponse.json(
        { error: 'Failed to process PDF file' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
